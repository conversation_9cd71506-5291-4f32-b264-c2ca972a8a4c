# file: kdiag/orchestrator.py
import json, os, re, shlex, subprocess, textwrap, time, uuid
from dataclasses import dataclass
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
from openai import OpenAI
from dotenv import load_dotenv
from datetime import datetime
import sys

from console_formatter import Consol<PERSON><PERSON><PERSON><PERSON><PERSON>, Logger, create_logger, log_info, log_success, log_warning, log_error, log_step, log_command, log_result

ALLOW = re.compile(r"^(kubectl)\s+(get|describe|logs|top|cluster-info|version|api-resources|api-versions|events|auth\s+can-i)\b[\s\S]*$")
DENY = re.compile(r"\b(apply|create|replace|patch|delete|edit|scale|rollout\s+restart|drain|cordon|uncordon|label|annotate|taint|exec(\s+-it)?|attach|port-forward|cp|proxy)\b")

MAX_BYTES_PER_OUT = 160_000  # ~160KB 上限，防止爆 token
REDACT_PATTERNS = [
    re.compile(r"(?i)token:[^\n]+"),
    re.compile(r"(?i)(password|secret|key):\s*[^\n]+"),
]

# SYSTEM_PROMPT_FILE = "system_prompt.md"
SYSTEM_PROMPT_FILE = "system_prompt_optimized.md"

# 创建专用的日志器
k8s_log = create_logger("K8S-Agent")

class MarkdownReportGenerator:
    """生成 Markdown 格式的诊断报告"""

    @staticmethod
    def generate_report(report_data: Dict[str, Any], audit_data: List[Dict[str, Any]]) -> str:
        """生成完整的 Markdown 诊断报告"""
        if not report_data:
            return "# 诊断报告\n\n❌ 未能生成有效的诊断报告"

        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        md_content = f"""# 🔍 Kubernetes 集群诊断报告

**生成时间**: {timestamp}
**诊断状态**: ✅ 完成

---

## 📋 执行摘要

{report_data.get('summary', '未提供摘要信息')}

---

## 🎯 根本原因分析

{report_data.get('root_cause', '未能确定根本原因')}

---

## 📊 影响范围

{report_data.get('impact', '影响范围未明确')}

---

## 🛠️ 建议修复步骤

"""

        next_steps = report_data.get('next_steps', [])
        if next_steps:
            for i, step in enumerate(next_steps, 1):
                md_content += f"{i}. {step}\n"
        else:
            md_content += "暂无具体修复建议\n"

        md_content += "\n---\n\n## 📎 诊断过程附录\n\n"

        # 添加诊断过程概览
        if audit_data:
            md_content += f"**总诊断轮次**: {len(audit_data)} 轮  \n"
            total_commands = sum(len(round_data.get('cmd_results', [])) for round_data in audit_data)
            md_content += f"**执行命令总数**: {total_commands} 条  \n\n"

            # 添加每轮的简要信息
            md_content += "### 诊断过程概览\n\n"
            for round_data in audit_data:
                round_num = round_data.get('round', 0)
                thought = round_data.get('plan', {}).get('thought', '无思考记录')
                cmd_count = len(round_data.get('cmd_results', []))
                md_content += f"- **第 {round_num} 轮**: {thought} ({cmd_count} 条命令)\n"

        # 添加证据引用
        appendix = report_data.get('appendix', [])
        if appendix:
            md_content += "\n### 关键证据引用\n\n"
            for evidence in appendix:
                md_content += f"- {evidence}\n"

        md_content += f"\n---\n\n*报告由 K8S 诊断专家自动生成于 {timestamp}*"

        return md_content

    @staticmethod
    def save_report(content: str, filename: str = None) -> str:
        """保存报告到文件"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"k8s_diagnosis_report_{timestamp}.md"

        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(content)
            return filename
        except Exception as e:
            print(f"保存报告失败: {e}")
            return ""

@dataclass
class CmdResult:
    id: str
    cmd: str
    rc: int
    stdout: str
    stderr: str
    duration_ms: int

class KubectlExecutor:
    def __init__(self, kubeconfig: Optional[str] = None, context: Optional[str] = None):
        self.kubeconfig = kubeconfig
        self.context = context

    def _build_env(self) -> Dict[str, str]:
        env = os.environ.copy()
        if self.kubeconfig:
            env["KUBECONFIG"] = self.kubeconfig
        return env

    def validate(self, cmd: str) -> Tuple[bool, str]:
        if not ALLOW.match(cmd):
            return False, "命令未匹配只读白名单"
        if DENY.search(cmd):
            return False, "命令命中禁止关键字"
        return True, "ok"

    def run(self, cmd: str, timeout_sec: int = 30) -> CmdResult:
        # ok, why = self.validate(cmd)
        cid = str(uuid.uuid4())
        # if not ok:
        #     return CmdResult(cid, cmd, 126, "", why, 0)
        args = shlex.split(cmd)
        t0 = time.time()
        try:
            out = subprocess.run(
                args,
                timeout=timeout_sec,
                env=self._build_env(),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
            )
            dur = int((time.time() - t0) * 1000)
            stdout, stderr = out.stdout, out.stderr
        except subprocess.TimeoutExpired:
            dur = int((time.time() - t0) * 1000)
            return CmdResult(cid, cmd, 124, "", f"Timeout after {timeout_sec}s", dur)

        # 截断与脱敏
        def redact(s: str) -> str:
            for p in REDACT_PATTERNS:
                s = p.sub("<redacted>", s)
            if len(s.encode("utf-8")) > MAX_BYTES_PER_OUT:
                s = s[:MAX_BYTES_PER_OUT // 2] + "\n...<truncated>...\n" + s[-MAX_BYTES_PER_OUT // 2 :]
            return s

        return CmdResult(cid, cmd, out.returncode, redact(stdout), redact(stderr), dur)

class LLMClient:
    def __init__(self, call_fn):
        self.call_fn = call_fn  # 注入式：与具体厂商解耦

    def plan(self, conversation: List[Dict[str, str]]) -> Dict[str, Any]:
        # 要求模型仅返回 JSON（由系统提示词约束）
        raw = self.call_fn(conversation)
        json_str = remove_markdown_marks(raw)
        # 只在调试模式下显示原始响应
        # k8s_log.debug(f"原始响应: {json_str}")
        try:
            plan = json.loads(json_str)
            assert isinstance(plan, dict) and "action" in plan
            return plan
        except Exception as e:
            # 强制模型重试：附带错误提示
            k8s_log.error(f"JSON 解析失败: {e}")
            return {"action": "continue", "commands": [], "thought": f"Invalid JSON: {e}", "finish_report": None}

class Orchestrator:
    def __init__(self, llm: LLMClient, executor: KubectlExecutor, prompt_file: str, max_rounds=12):
        self.llm = llm
        self.exec = executor
        self.max_rounds = max_rounds
        self.audit: List[Dict[str, Any]] = []
        self.system_prompt = Path(prompt_file).read_text(encoding="utf-8")

    def run(self, user_goal: str) -> Dict[str, Any]:
        print(ConsoleFormatter.section("开始诊断分析", "magnifying"))

        conversation = [
            {"role": "system", "content": self.system_prompt},
            {"role": "user", "content": user_goal}
        ]

        for r in range(1, self.max_rounds + 1):
            plan = self.llm.plan(conversation)
            thought = plan.get('thought', '正在分析...')

            # 使用简化的日志方法
            k8s_log.step(r, thought)

            # 显示进度条
            progress = ConsoleFormatter.progress_bar(r, self.max_rounds)
            print(f"  {progress}")

            round_rec = {"round": r, "plan": plan, "cmd_results": []}

            if plan.get("action") == "finish":
                k8s_log.success("诊断完成，正在生成报告...")
                self.audit.append(round_rec)
                return {"status": "done", "audit": self.audit, "report": plan.get("finish_report")}

            commands = plan.get("commands", [])[:5]
            if commands:
                k8s_log.info(f"准备执行 {len(commands)} 条命令")

            for item in commands:
                cmd = item.get("run", "").strip()
                reason = item.get("why", "").strip()

                # 使用简化的命令日志
                k8s_log.command(cmd, reason)

                timeout = int(item.get("timeout_sec", 30))
                res = self.exec.run(cmd, timeout)

                # 使用简化的结果日志
                success = res.rc == 0
                error_msg = ""
                if not success and res.stderr:
                    # 截断长错误信息
                    error_msg = res.stderr[:200] + "..." if len(res.stderr) > 200 else res.stderr

                k8s_log.result(success, error_msg, res.duration_ms)

                round_rec["cmd_results"].append({
                    "id": item.get("id"),
                    "cmd": res.cmd,
                    "rc": res.rc,
                    "stdout": res.stdout,
                    "stderr": res.stderr,
                    "duration_ms": res.duration_ms,
                })

            # 将本轮结果喂给模型（工具角色）
            tool_msg = json.dumps(round_rec["cmd_results"], ensure_ascii=False)
            conversation.append({"role": "tool", "content": tool_msg})
            self.audit.append(round_rec)

        # 达到最大轮次时的处理
        k8s_log.warning("已达到最大诊断轮次，尝试生成最终报告...")
        r = self.max_rounds + 1
        conversation.append({"role": "Orchestrator", "content": "请输出最终诊断报告"})

        plan = self.llm.plan(conversation)
        round_rec = {"round": r, "plan": plan, "cmd_results": []}

        if plan.get("action") == "finish":
            self.audit.append(round_rec)
            return {"status": "done", "audit": self.audit, "report": plan.get("finish_report")}
        else:
            return {"status": "max_rounds_reached", "audit": self.audit}


def remove_markdown_marks(text: str):
    """
    去除Markdown代码块的前后标记（如```json和```），返回纯代码内容。
    若输入不是有效的代码块，则返回原始文本。
    """
    stripped_text = text.strip()
    if stripped_text.startswith('```') and stripped_text.endswith('```'):
        lines = stripped_text.splitlines()
        if len(lines) < 2:
            return ""  # 只有标记没有内容
        return '\n'.join(lines[1:-1])  # 去除首行标记和末行标记
    return text  # 非代码块返回原始文本


def get_env(key: str) -> str:
    """Load the environment from an environment variable."""
    load_dotenv()
    env = os.getenv(key)
    if not env:
        raise ValueError("未找到 {key} 环境变量，请在 .env 文件中设置。")
    return env


def get_llm_api() -> str:
    return get_env("API_URL")


def get_api_key() -> str:
    return get_env("API_KEY")


def get_llm_model() -> str:
    return get_env("LLM_MODEL")


if __name__ == "__main__":
    try:
        client = OpenAI(base_url=get_llm_api(), api_key=get_api_key())
        model = get_llm_model()

        # 示例：将任意 LLM 厂商的调用封装成 call_fn(conversation)->str
        def llm_call_fn(conversation: List[Dict[str, str]]) -> str:
            # 返回值必须是严格的 JSON 字符串，系统提示词已规定
            response = client.chat.completions.create(
                model=model,
                messages=conversation,
                stream=False)
            return response.choices[0].message.content

        llm = LLMClient(llm_call_fn)
        ex = KubectlExecutor()
        oc = Orchestrator(llm, ex, SYSTEM_PROMPT_FILE)

        # 美化的欢迎界面
        print(ConsoleFormatter.header("🚀 Kubernetes 集群诊断助手"))
        k8s_log.info(f"当前使用模型: {ConsoleFormatter.colored(model, 'white')}")
        k8s_log.info("支持的诊断场景:")

        scenarios = [
            "部分 Pod 无法启动",
            "节点显示 NotReady 状态",
            "服务无法访问",
            "API 服务器响应缓慢",
            "资源配额不足错误",
            "网络连接问题",
            "存储挂载失败"
        ]

        for scenario in scenarios:
            print(f"  • {scenario}")

        print(f"\n{ConsoleFormatter.colored('─' * 60, 'cyan')}")

        user_query = input(f"\n{ConsoleFormatter.ICONS['magnifying']} 请输入您要诊断的问题: ")
        if not user_query.strip():
            k8s_log.error("未提供问题描述，退出程序")
            sys.exit(0)

        k8s_log.info(f"🎯 开始诊断问题: {ConsoleFormatter.colored(user_query, 'yellow')}")

        # 执行诊断
        result = oc.run(user_query)

        # 生成和显示报告
        print(ConsoleFormatter.section("生成诊断报告", "report"))

        if result.get("status") == "done" and result.get("report"):
            # 生成 Markdown 报告
            markdown_content = MarkdownReportGenerator.generate_report(
                result["report"],
                result.get("audit", [])
            )

            # 保存报告文件
            report_filename = MarkdownReportGenerator.save_report(markdown_content)
            if report_filename:
                k8s_log.success(f"报告已保存到: {report_filename}")

            # 在控制台显示报告摘要
            print(ConsoleFormatter.header("📋 诊断报告摘要"))
            report = result["report"]

            print(f"{ConsoleFormatter.colored('📋 执行摘要:', 'bold')}")
            print(f"  {report.get('summary', '未提供摘要信息')}\n")

            print(f"{ConsoleFormatter.colored('🎯 根本原因:', 'bold')}")
            print(f"  {report.get('root_cause', '未能确定根本原因')}\n")

            print(f"{ConsoleFormatter.colored('📊 影响范围:', 'bold')}")
            print(f"  {report.get('impact', '影响范围未明确')}\n")

            print(f"{ConsoleFormatter.colored('🛠️ 建议修复步骤:', 'bold')}")
            next_steps = report.get('next_steps', [])
            if next_steps:
                for i, step in enumerate(next_steps, 1):
                    print(f"  {i}. {step}")
            else:
                print("  暂无具体修复建议")

            k8s_log.info(f"📎 详细报告请查看: {report_filename}")

        else:
            k8s_log.error("诊断未能完成或未生成有效报告")
            if result.get("status") == "max_rounds_reached":
                k8s_log.warning("已达到最大诊断轮次，可能需要人工介入")

        print(f"\n{ConsoleFormatter.header('🎉 诊断任务完成')}")

    except KeyboardInterrupt:
        k8s_log.warning("\n用户中断操作")
        sys.exit(0)
    except Exception as e:
        k8s_log.error(f"程序执行出错: {e}")
        sys.exit(1)