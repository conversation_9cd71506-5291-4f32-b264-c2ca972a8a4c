# file: kdiag/orchestrator.py
import json, os, re, shlex, subprocess, textwrap, time, uuid
from dataclasses import dataclass
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
from openai import OpenAI
from dotenv import load_dotenv

ALLOW = re.compile(r"^(kubectl)\s+(get|describe|logs|top|cluster-info|version|api-resources|api-versions|events|auth\s+can-i)\b[\s\S]*$")
DENY = re.compile(r"\b(apply|create|replace|patch|delete|edit|scale|rollout\s+restart|drain|cordon|uncordon|label|annotate|taint|exec(\s+-it)?|attach|port-forward|cp|proxy)\b")

MAX_BYTES_PER_OUT = 160_000  # ~160KB 上限，防止爆 token
REDACT_PATTERNS = [
    re.compile(r"(?i)token:[^\n]+"),
    re.compile(r"(?i)(password|secret|key):\s*[^\n]+"),
]

SYSTEM_PROMPT_FILE = "system_prompt.txt"

@dataclass
class CmdResult:
    id: str
    cmd: str
    rc: int
    stdout: str
    stderr: str
    duration_ms: int

class KubectlExecutor:
    def __init__(self, kubeconfig: Optional[str] = None, context: Optional[str] = None):
        self.kubeconfig = kubeconfig
        self.context = context

    def _build_env(self) -> Dict[str, str]:
        env = os.environ.copy()
        if self.kubeconfig:
            env["KUBECONFIG"] = self.kubeconfig
        return env

    def validate(self, cmd: str) -> Tuple[bool, str]:
        if not ALLOW.match(cmd):
            return False, "命令未匹配只读白名单"
        if DENY.search(cmd):
            return False, "命令命中禁止关键字"
        return True, "ok"

    def run(self, cmd: str, timeout_sec: int = 30) -> CmdResult:
        # ok, why = self.validate(cmd)
        cid = str(uuid.uuid4())
        # if not ok:
        #     return CmdResult(cid, cmd, 126, "", why, 0)
        args = shlex.split(cmd)
        t0 = time.time()
        try:
            out = subprocess.run(
                args,
                timeout=timeout_sec,
                env=self._build_env(),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
            )
            dur = int((time.time() - t0) * 1000)
            stdout, stderr = out.stdout, out.stderr
        except subprocess.TimeoutExpired:
            dur = int((time.time() - t0) * 1000)
            return CmdResult(cid, cmd, 124, "", f"Timeout after {timeout_sec}s", dur)

        # 截断与脱敏
        def redact(s: str) -> str:
            for p in REDACT_PATTERNS:
                s = p.sub("<redacted>", s)
            if len(s.encode("utf-8")) > MAX_BYTES_PER_OUT:
                s = s[:MAX_BYTES_PER_OUT // 2] + "\n...<truncated>...\n" + s[-MAX_BYTES_PER_OUT // 2 :]
            return s

        return CmdResult(cid, cmd, out.returncode, redact(stdout), redact(stderr), dur)

class LLMClient:
    def __init__(self, call_fn):
        self.call_fn = call_fn  # 注入式：与具体厂商解耦

    def plan(self, conversation: List[Dict[str, str]]) -> Dict[str, Any]:
        # 要求模型仅返回 JSON（由系统提示词约束）
        raw = self.call_fn(conversation)
        json_str = remove_markdown_marks(raw)
        print(f"原始响应: {json_str}")
        try:
            plan = json.loads(json_str)
            assert isinstance(plan, dict) and "action" in plan
            return plan
        except Exception as e:
            # 强制模型重试：附带错误提示
            return {"action": "continue", "commands": [], "thought": f"Invalid JSON: {e}", "finish_report": None}

class Orchestrator:
    def __init__(self, llm: LLMClient, executor: KubectlExecutor, prompt_file: str, max_rounds=12):
        self.llm = llm
        self.exec = executor
        self.max_rounds = max_rounds
        self.audit: List[Dict[str, Any]] = []
        self.system_prompt = Path(prompt_file).read_text(encoding="utf-8")

    def run(self, user_goal: str) -> Dict[str, Any]:
        conversation = [
            {"role": "system", "content": self.system_prompt},
            {"role": "user", "content": user_goal}
        ]
        for r in range(1, self.max_rounds + 1):
            print(f"开始第 {r} 轮检查 ====================")
            plan = self.llm.plan(conversation)
            print(f" ==> {plan.get('thought')}")
            round_rec = {"round": r, "plan": plan, "cmd_results": []}
            if plan.get("action") == "finish":
                self.audit.append(round_rec)
                return {"status": "done", "audit": self.audit, "report": plan.get("finish_report")}

            for item in plan.get("commands", [])[:5]:
                cmd = item.get("run", "").strip()
                reason = item.get("why", "").strip()
                print(f"{reason} ==> 执行命令：\"{cmd}\"")
                timeout = int(item.get("timeout_sec", 30))
                res = self.exec.run(cmd, timeout)
                print(f"命令执行结果：\n{res}")
                round_rec["cmd_results"].append({
                    "id": item.get("id"),
                    "cmd": res.cmd,
                    "rc": res.rc,
                    "stdout": res.stdout,
                    "stderr": res.stderr,
                    "duration_ms": res.duration_ms,
                })

            # 将本轮结果喂给模型（工具角色）
            tool_msg = json.dumps(round_rec["cmd_results"], ensure_ascii=False)
            conversation.append({"role": "tool", "content": tool_msg})
            self.audit.append(round_rec)

        return {"status": "max_rounds_reached", "audit": self.audit}


def remove_markdown_marks(text: str):
    """
    去除Markdown代码块的前后标记（如```json和```），返回纯代码内容。
    若输入不是有效的代码块，则返回原始文本。
    """
    stripped_text = text.strip()
    if stripped_text.startswith('```') and stripped_text.endswith('```'):
        lines = stripped_text.splitlines()
        if len(lines) < 2:
            return ""  # 只有标记没有内容
        return '\n'.join(lines[1:-1])  # 去除首行标记和末行标记
    return text  # 非代码块返回原始文本


def get_env(key: str) -> str:
    """Load the environment from an environment variable."""
    load_dotenv()
    env = os.getenv(key)
    if not env:
        raise ValueError("未找到 {key} 环境变量，请在 .env 文件中设置。")
    return env


def get_llm_api() -> str:
    return get_env("API_URL")


def get_api_key() -> str:
    return get_env("API_KEY")


def get_llm_model() -> str:
    return get_env("LLM_MODEL")


if __name__ == "__main__":
    client = OpenAI(base_url=get_llm_api(), api_key=get_api_key())
    model = get_llm_model()
    # 示例：将任意 LLM 厂商的调用封装成 call_fn(conversation)->str
    def llm_call_fn(conversation: List[Dict[str, str]]) -> str:
        # 返回值必须是严格的 JSON 字符串，系统提示词已规定
        response = client.chat.completions.create(
            model=model, 
            messages=conversation,
            stream=False)
        return response.choices[0].message.content

    llm = LLMClient(llm_call_fn)
    ex = KubectlExecutor()
    oc = Orchestrator(llm, ex, SYSTEM_PROMPT_FILE)

    print("="*50)
    print("Kubernetes集群诊断助手")
    print(f"当前使用模型: {model}")
    print("="*50)
    print("\n请描述您遇到的集群问题（例如：")
    print("- 部分Pod无法启动")
    print("- 节点显示NotReady状态")
    print("- 服务无法访问")
    print("- API服务器响应缓慢")
    print("- 资源配额不足错误")
    print("="*50)

    user_query = input("\n请输入您要诊断的问题: ")
    if not user_query.strip():
        print("未提供问题描述，退出程序")
        exit(0)
        
    print(f"\n开始诊断问题: '{user_query}'...")

    result = oc.run(user_query)

    print("\n" + "="*50)
    print("任务完成，诊断报告如下：")
    print("="*50)
    print(json.dumps(result, ensure_ascii=False, indent=2))