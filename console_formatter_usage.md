# ConsoleFormatter 使用指南

## 概述

`ConsoleFormatter` 是一个独立的 Python 模块，提供美观的控制台输出和简化的日志功能。它支持彩色输出、图标显示和多种格式化选项。

## 安装和导入

```python
# 基本导入
from console_formatter import ConsoleFormatter

# 导入简化的日志方法
from console_formatter import log_info, log_success, log_warning, log_error, log_step, log_command, log_result

# 导入自定义日志器
from console_formatter import create_logger
```

## 基本使用

### 1. 简化的全局日志方法

```python
# 基本日志输出
log_info("这是一条信息")           # ℹ️ 这是一条信息
log_success("操作成功")           # ✅ 操作成功
log_warning("注意事项")           # ⚠️ 注意事项
log_error("发生错误")             # ❌ 发生错误
log_debug("调试信息")             # • 调试信息

# 步骤日志
log_step(1, "开始第一步")         # → 第 1 步 开始第一步

# 命令执行日志
log_command("kubectl get pods", "检查Pod状态")
# ℹ️ 检查Pod状态
# ⚡ $ kubectl get pods

# 执行结果日志
log_result(True, "找到 5 个Pod", 250)    # ✓ 成功 (250ms) - 找到 5 个Pod
log_result(False, "连接失败")            # ✗ 失败 - 连接失败
```

### 2. 自定义日志器

```python
# 创建带前缀的日志器
k8s_log = create_logger("K8S", show_timestamp=True)

k8s_log.info("集群诊断开始")      # [14:30:22] [K8S] ℹ️ 集群诊断开始
k8s_log.success("节点状态正常")   # [14:30:23] [K8S] ✅ 节点状态正常
k8s_log.warning("发现网络问题")   # [14:30:24] [K8S] ⚠️ 发现网络问题
k8s_log.error("连接失败")        # [14:30:25] [K8S] ❌ 连接失败

# 专用方法
k8s_log.step(1, "分析集群状态")
k8s_log.command("kubectl get nodes", "检查节点")
k8s_log.result(True, "3个节点正常", 180)
```

### 3. 高级格式化

```python
# 标题和章节
print(ConsoleFormatter.header("🚀 应用标题"))
print(ConsoleFormatter.section("开始处理", "gear"))

# 进度条
for i in range(1, 6):
    progress = ConsoleFormatter.progress_bar(i, 5)
    print(f"处理中... {progress}")

# 彩色文本
colored_text = ConsoleFormatter.colored("重要信息", "red")
print(f"注意: {colored_text}")

# 直接格式化方法
print(ConsoleFormatter.success("操作完成"))
print(ConsoleFormatter.error("发生错误"))
print(ConsoleFormatter.warning("需要注意"))
print(ConsoleFormatter.info("提示信息"))
```

## 实际应用示例

### 示例 1: 简单的任务执行

```python
from console_formatter import log_info, log_step, log_command, log_result, log_success

def deploy_application():
    log_info("开始部署应用")
    
    log_step(1, "构建Docker镜像")
    log_command("docker build -t myapp:latest .", "构建应用镜像")
    log_result(True, "镜像构建完成", 15000)
    
    log_step(2, "推送到仓库")
    log_command("docker push myapp:latest", "推送镜像")
    log_result(True, "推送成功", 8000)
    
    log_step(3, "部署到K8S")
    log_command("kubectl apply -f deployment.yaml", "应用部署配置")
    log_result(True, "部署完成", 3000)
    
    log_success("应用部署成功完成")
```

### 示例 2: 带错误处理的复杂任务

```python
from console_formatter import create_logger, ConsoleFormatter

def diagnose_cluster():
    # 创建专用日志器
    diag_log = create_logger("DIAG", show_timestamp=True)
    
    print(ConsoleFormatter.header("🔍 集群诊断工具"))
    
    try:
        diag_log.info("开始集群诊断")
        
        # 检查节点
        diag_log.step(1, "检查集群节点")
        diag_log.command("kubectl get nodes")
        # 模拟执行结果
        diag_log.result(True, "发现 3 个节点", 200)
        
        # 检查Pod
        diag_log.step(2, "检查Pod状态")
        diag_log.command("kubectl get pods --all-namespaces")
        diag_log.result(False, "部分Pod异常", 500)
        
        # 深入分析
        diag_log.step(3, "分析异常原因")
        diag_log.warning("发现网络配置问题")
        
        diag_log.success("诊断完成，生成报告")
        
    except Exception as e:
        diag_log.error(f"诊断过程出错: {e}")
```

### 示例 3: 集成到现有项目

```python
# 在现有的类中使用
class KubernetesManager:
    def __init__(self):
        # 创建专用日志器
        self.log = create_logger("K8S-MGR")
    
    def scale_deployment(self, name, replicas):
        self.log.info(f"开始扩缩容部署: {name}")
        
        cmd = f"kubectl scale deployment {name} --replicas={replicas}"
        self.log.command(cmd, f"设置副本数为 {replicas}")
        
        try:
            # 执行命令的逻辑
            success = self._execute_kubectl(cmd)
            self.log.result(success, f"副本数已更新为 {replicas}" if success else "扩缩容失败")
            
            if success:
                self.log.success(f"部署 {name} 扩缩容完成")
            else:
                self.log.error(f"部署 {name} 扩缩容失败")
                
        except Exception as e:
            self.log.error(f"扩缩容过程出错: {e}")
```

## 颜色和图标参考

### 可用颜色
- `red`: 红色（错误、警告）
- `green`: 绿色（成功、正常）
- `yellow`: 黄色（警告、注意）
- `blue`: 蓝色（信息、标题）
- `purple`: 紫色（步骤、特殊）
- `cyan`: 青色（提示、链接）
- `white`: 白色（强调）
- `gray`: 灰色（次要信息）

### 可用图标
- `success`: ✅ 成功
- `error`: ❌ 错误
- `warning`: ⚠️ 警告
- `info`: ℹ️ 信息
- `thinking`: 🤔 思考
- `executing`: ⚡ 执行
- `report`: 📋 报告
- `rocket`: 🚀 启动
- `gear`: ⚙️ 设置
- `magnifying`: 🔍 搜索
- `clock`: ⏰ 时间
- `check`: ✓ 检查
- `cross`: ✗ 失败
- `arrow`: → 箭头
- `bullet`: • 项目符号

## 最佳实践

1. **统一日志器**: 为每个模块创建专用的日志器
2. **合理使用颜色**: 遵循颜色语义（红色=错误，绿色=成功）
3. **适度使用图标**: 不要过度使用，保持简洁
4. **时间戳控制**: 只在需要时启用时间戳
5. **错误处理**: 重要错误使用 `log_error`，输出到 stderr

## 性能考虑

- 颜色输出在某些终端可能较慢，可以通过环境变量控制
- 大量日志输出时考虑使用缓冲
- 在生产环境中可以禁用颜色和图标

## 兼容性

- 支持大多数现代终端
- Windows 10+ 支持 ANSI 颜色
- 在不支持颜色的终端中会自动降级
