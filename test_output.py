#!/usr/bin/env python3
"""
测试美化输出效果的脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agent import ConsoleFormatter, MarkdownReportGenerator
from datetime import datetime

def test_console_output():
    """测试控制台美化输出"""
    print(ConsoleFormatter.header("🚀 Kubernetes 集群诊断助手"))
    
    print(f"{ConsoleFormatter.ICONS['gear']} {ConsoleFormatter.colored('当前使用模型:', 'cyan')} {ConsoleFormatter.colored('gpt-4', 'white')}")
    
    print(ConsoleFormatter.section("开始诊断分析", "magnifying"))
    
    print(ConsoleFormatter.step(1, "分析集群基本状态"))
    print(f"  {ConsoleFormatter.progress_bar(1, 5)}")
    
    print(f"\n{ConsoleFormatter.command('检查节点状态', 'kubectl get nodes -o wide')}")
    print(f"    {ConsoleFormatter.colored('✓ 执行成功 (245ms)', 'green')}")
    
    print(f"\n{ConsoleFormatter.command('检查Pod状态', 'kubectl get pods --all-namespaces')}")
    print(f"    {ConsoleFormatter.error('执行失败 (rc=1)')}")
    print(f"    {ConsoleFormatter.colored('错误: connection refused', 'red')}")
    
    print(ConsoleFormatter.step(2, "深入分析问题原因"))
    print(f"  {ConsoleFormatter.progress_bar(2, 5)}")
    
    print(f"\n{ConsoleFormatter.success('诊断完成，正在生成报告...')}")
    
    print(ConsoleFormatter.section("生成诊断报告", "report"))

def test_markdown_report():
    """测试 Markdown 报告生成"""
    # 模拟报告数据
    report_data = {
        "summary": "检测到集群中存在网络连接问题，导致部分Pod无法正常启动",
        "root_cause": "kube-proxy配置错误，导致Service网络不通。具体表现为iptables规则缺失，Pod间通信失败。",
        "impact": "影响范围：default命名空间下的web服务，约50%的用户请求失败",
        "next_steps": [
            "重启所有节点上的kube-proxy服务",
            "检查并修复CNI网络插件配置",
            "验证Service和Endpoint的正确性",
            "监控网络连接恢复情况"
        ],
        "appendix": [
            "kubectl get nodes 输出显示节点状态正常",
            "kubectl get pods 显示多个Pod处于Pending状态",
            "kubectl logs kube-proxy 显示配置加载失败"
        ]
    }
    
    # 模拟审计数据
    audit_data = [
        {
            "round": 1,
            "plan": {"thought": "检查集群基本状态"},
            "cmd_results": [
                {"cmd": "kubectl get nodes", "rc": 0},
                {"cmd": "kubectl get pods --all-namespaces", "rc": 1}
            ]
        },
        {
            "round": 2,
            "plan": {"thought": "分析网络问题"},
            "cmd_results": [
                {"cmd": "kubectl get svc", "rc": 0},
                {"cmd": "kubectl logs kube-proxy", "rc": 0}
            ]
        }
    ]
    
    # 生成报告
    markdown_content = MarkdownReportGenerator.generate_report(report_data, audit_data)
    
    # 保存测试报告
    filename = MarkdownReportGenerator.save_report(markdown_content, "test_report.md")
    
    print(f"\n{ConsoleFormatter.success(f'测试报告已生成: {filename}')}")
    
    # 显示报告预览
    print(ConsoleFormatter.header("📋 报告预览"))
    print(markdown_content[:500] + "..." if len(markdown_content) > 500 else markdown_content)

if __name__ == "__main__":
    print("🧪 测试美化输出效果\n")
    
    test_console_output()
    print("\n" + "="*60 + "\n")
    test_markdown_report()
    
    print(f"\n{ConsoleFormatter.header('🎉 测试完成')}")
